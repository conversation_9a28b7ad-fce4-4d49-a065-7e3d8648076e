# 2025-07-10 #9

## Commit: ba507ff
refactor(auth): separate login and register into dedicated routes

- Extract registration logic from /login into dedicated /register route
- Add validation schemas for username and password with proper sanitization
- Improve form UX with better error handling and field persistence
- Add clear navigation between login and register pages
- Maintain consistent styling and accessibility across auth forms

Files changed:
- src/lib/server/validation.ts
- src/routes/(auth)/login/+page.server.ts
- src/routes/(auth)/login/+page.svelte
- src/routes/(auth)/register/+page.server.ts
- src/routes/(auth)/register/+page.svelte


## Summary
Refactored authentication system by separating combined login/register functionality into dedicated routes with improved validation and UX.

## Changes

### Refactor: Authentication Route Separation
**What:** Extracted registration logic from the combined `/login` route into a dedicated `/register` route, added centralized validation schemas, and improved form UX.
**Why:** The previous implementation had both login and register functionality mixed in a single route, making it harder to maintain and reason about. Separation improves code organization and allows for route-specific optimizations.
**How:** Created new validation schemas using Zod, extracted registration logic into dedicated server and client files, and enhanced form handling with better error messages and field persistence.

```typescript
// New validation schemas
export const usernameSchema = z.string()
  .transform((val) => val.trim().toLowerCase().replace(/\s+/g, ''))
  .pipe(
    z.string()
      .min(3, 'Username must be at least 3 characters')
      .max(31, 'Username must be at most 31 characters')
      .regex(/^[a-z0-9_-]+$/, 'Username can only contain lowercase letters, numbers, hyphens, and underscores')
  );

export const registerSchema = zfd.formData({
  username: zfd.text(usernameSchema),
  password: zfd.text(passwordSchema)
});
```

## Notes
- Registration and login now have separate validation schemas, allowing for future divergence (e.g., email requirements for registration)
- Form data persistence ensures better UX when validation fails
- Autocomplete attributes properly set for password managers
- Clear navigation between auth routes improves user flow
- Consistent styling maintained across both forms
