import { test, expect } from '@playwright/test';

test.describe('Authentication flows', () => {
	test('user can register and login', async ({ page }) => {
		const username = `testuser_${Date.now()}`;
		const password = 'password123';

		// Go to register page and register new user
		await page.goto('/register');
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', password);
		await page.click('button:has-text("Register")');

		// Should redirect to dashboard
		await expect(page).toHaveURL('/dashboard');
		await expect(page.locator('h1')).toContainText(`Hi, ${username}!`);
		await expect(page.locator('p')).toContainText('Your user ID is');
	});

	test('user can login with existing credentials', async ({ page }) => {
		const username = `loginuser_${Date.now()}`;
		const password = 'password123';

		// First register
		await page.goto('/register');
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', password);
		await page.click('button:has-text("Register")');
		await expect(page).toHaveURL('/dashboard');

		// Logout
		await page.click('button:has-text("Sign out")'); // Sign out button
		await expect(page).toHaveURL('/login');

		// Login again
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', password);
		await page.click('button:has-text("Login")');

		// Should be back on dashboard
		await expect(page).toHaveURL('/dashboard');
		await expect(page.locator('h1')).toContainText(`Hi, ${username}!`);
	});

	test('user can logout', async ({ page }) => {
		const username = `logoutuser_${Date.now()}`;
		const password = 'password123';

		// Register and login
		await page.goto('/register');
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', password);
		await page.click('button:has-text("Register")');
		await expect(page).toHaveURL('/dashboard');

		// Logout
		await page.click('button:has-text("Sign out")'); // Sign out button
		await expect(page).toHaveURL('/login');

		// Should not be able to access dashboard without login
		await page.goto('/dashboard');
		await expect(page).toHaveURL('/login');
	});

	test('shows error for invalid login credentials', async ({ page }) => {
		// Go to login page and try to login with non-existent user
		await page.goto('/login');
		await page.fill('input[name="username"]', 'nonexistentuser');
		await page.fill('input[name="password"]', 'wrongpassword');
		await page.click('button:has-text("Login")');

		// Should stay on login page and show error
		await expect(page).toHaveURL('/login');
		await expect(page.getByText('Incorrect username or password')).toBeVisible();
	});

	test('shows error for duplicate username registration', async ({ page }) => {
		const username = `duplicateuser_${Date.now()}`;
		const password = 'password123';

		// Register first user
		await page.goto('/register');
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', password);
		await page.click('button:has-text("Register")');
		await expect(page).toHaveURL('/dashboard');

		// Logout
		await page.click('button:has-text("Sign out")');
		await expect(page).toHaveURL('/login');

		// Try to register with same username
		await page.goto('/register');
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', 'differentpassword');
		await page.click('button:has-text("Register")');

		// Should stay on register page and show error
		await expect(page).toHaveURL('/register');
		await expect(page.locator('text=Username already taken')).toBeVisible();
	});

	test('shows error for invalid username format', async ({ page }) => {
		// Go to register page and try username that's too short
		await page.goto('/register');
		await page.fill('input[name="username"]', 'ab');
		await page.fill('input[name="password"]', 'password123');
		await page.click('button:has-text("Register")');

		// Should stay on register page and show validation error
		await expect(page).toHaveURL('/register');
		await expect(page.getByText('Username must be at least 3 characters')).toBeVisible();
	});

	test('shows error for invalid password format', async ({ page }) => {
		// Go to register page and try password that's too short
		await page.goto('/register');
		await page.fill('input[name="username"]', 'validuser');
		await page.fill('input[name="password"]', '123');
		await page.click('button:has-text("Register")');

		// Should stay on register page and show validation error
		await expect(page).toHaveURL('/register');
		await expect(page.getByText('Password must be at least 6 characters')).toBeVisible();
	});

	test('protected routes redirect to login when not authenticated', async ({ page }) => {
		// Try to access dashboard without login
		await page.goto('/dashboard');
		await expect(page).toHaveURL('/login');
	});

	test('authenticated users are redirected from login page', async ({ page }) => {
		const username = `redirectuser_${Date.now()}`;
		const password = 'password123';

		// Register and login
		await page.goto('/register');
		await page.fill('input[name="username"]', username);
		await page.fill('input[name="password"]', password);
		await page.click('button:has-text("Register")');
		await expect(page).toHaveURL('/dashboard');

		// Try to go back to login page
		await page.goto('/login');
		// Should be redirected to dashboard
		await expect(page).toHaveURL('/dashboard');
	});
});
