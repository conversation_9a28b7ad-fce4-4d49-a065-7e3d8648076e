import { z } from 'zod/v4';
import { zfd } from 'zod-form-data';

export const usernameSchema = z.string()
  .transform((val) => val.trim().toLowerCase().replace(/\s+/g, ''))
  .pipe(
    z.string()
      .min(3, 'Username must be at least 3 characters')
      .max(31, 'Username must be at most 31 characters')
      .regex(/^[a-z0-9_-]+$/, 'Username can only contain lowercase letters, numbers, hyphens, and underscores')
  );

export const passwordSchema = z.string()
  .transform((val) => val.trim())
  .pipe(
    z.string()
      .min(6, 'Password must be at least 6 characters')
      .max(255, 'Password must be at most 255 characters')
  );

export const loginSchema = zfd.formData({
  username: zfd.text(usernameSchema),
  password: zfd.text(passwordSchema)
});

// Separate schema for registration allows for future fields
export const registerSchema = zfd.formData({
  username: zfd.text(usernameSchema),
  password: zfd.text(passwordSchema)
});