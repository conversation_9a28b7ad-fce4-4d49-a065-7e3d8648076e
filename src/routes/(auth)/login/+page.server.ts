import { verify } from '@node-rs/argon2';
import { fail, redirect } from '@sveltejs/kit';
import * as auth from '$lib/server/auth';
import { loginSchema } from '$lib/server/validation';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  if (event.locals.user) {
    return redirect(302, '/dashboard');
  }
  return {};
};

export const actions: Actions = {
  default: async (event) => {
    const formData = await event.request.formData();
    const result = loginSchema.safeParse(formData);

    if (!result.success) {
      return fail(400, {
        data: Object.fromEntries(formData),
        message: result.error.issues[0].message
      });
    }
    
    const { username, password } = result.data;

    const user = await auth.getUserByUsername(username);
    if (!user) {
      return fail(400, { 
        data: { username },
        message: 'Incorrect username or password' 
      });
    }

    const validPassword = await verify(user.passwordHash, password, {
      memoryCost: 19456,
      timeCost: 2,
      outputLen: 32,
      parallelism: 1
    });

    if (!validPassword) {
      return fail(400, { 
        data: { username },
        message: 'Incorrect username or password' 
      });
    }

    const sessionToken = auth.generateSessionToken();
    const session = await auth.createSession(sessionToken, user.id);
    auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);

    return redirect(302, '/dashboard');
  }
};
