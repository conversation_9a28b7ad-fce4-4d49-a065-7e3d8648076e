<script lang="ts">
	import { enhance } from '$app/forms';

	// Correctly type `form` to handle data returned on validation failure
	let { form }: { form?: { data?: { username?: string }; message?: string } } = $props();
</script>

<div class="auth-container">
	<h1>Create Account</h1>

	<form method="post" use:enhance>
		<label>
			Username
			<input
				name="username"
				autocomplete="username"
				value={form?.data?.username ?? ''}
				class="mt-1 rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
			/>
		</label>
		<label>
			Password
			<input
				type="password"
				name="password"
				autocomplete="new-password"
				class="mt-1 rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
			/>
		</label>
		<button class="rounded-md bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700">
			Register
		</button>
	</form>

	{#if form?.message}
		<p class="error">{form.message}</p>
	{/if}

	<p class="mt-4">
		Already have an account? <a href="/login" class="text-blue-600 hover:underline">Login here</a>
	</p>
</div>

<style>
	.auth-container {
		max-width: 400px;
		margin: 2rem auto;
	}
	.error {
		color: red;
		margin-top: 1rem;
	}
</style>