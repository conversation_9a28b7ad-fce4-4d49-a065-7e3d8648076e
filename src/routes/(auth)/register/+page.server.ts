import { hash } from '@node-rs/argon2';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { fail, redirect } from '@sveltejs/kit';
import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { registerSchema } from '$lib/server/validation';
import type { Actions, PageServerLoad } from './$types';

interface PostgresError {
  code: string;
}

export const load: PageServerLoad = async (event) => {
  if (event.locals.user) {
    return redirect(302, '/dashboard');
  }
  return {};
};

export const actions: Actions = {
  default: async (event) => {
    const formData = await event.request.formData();
    const result = registerSchema.safeParse(formData);

    if (!result.success) {
      return fail(400, {
        data: Object.fromEntries(formData),
        message: result.error.issues[0].message
      });
    }

    const { username, password } = result.data;

    const userId = generateUserId();
    const passwordHash = await hash(password, {
      memoryCost: 19456,
      timeCost: 2,
      outputLen: 32,
      parallelism: 1
    });

    try {
      await db.insert(table.user).values({ id: userId, username, passwordHash });

      const sessionToken = auth.generateSessionToken();
      const session = await auth.createSession(sessionToken, userId);
      auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
    } catch (e: unknown) {
      if (e && typeof e === 'object' && 'code' in e && (e as PostgresError).code === '23505') {
        return fail(400, { data: { username }, message: 'Username already taken' });
      }
      return fail(500, { message: 'An unexpected error occurred. Please try again.' });
    }
    
    return redirect(302, '/dashboard');
  }
};

function generateUserId() {
  const bytes = crypto.getRandomValues(new Uint8Array(15));
  return encodeBase32LowerCase(bytes);
}
